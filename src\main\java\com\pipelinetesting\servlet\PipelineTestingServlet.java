package com.pipelinetesting.servlet;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Main servlet for the Pipeline Testing Web Application.
 * Handles requests and serves the main page with pipeline testing content.
 */
@WebServlet(name = "PipelineTestingServlet", urlPatterns = {"/", "/home"})
public class PipelineTestingServlet extends HttpServlet {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Handles GET requests to display the main pipeline testing page.
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // Set response content type
        response.setContentType("text/html;charset=UTF-8");
        
        // Prepare data for the JSP page
        preparePageData(request);
        
        // Forward to the main JSP page
        request.getRequestDispatcher("/index.jsp").forward(request, response);
    }
    
    /**
     * Handles POST requests (currently redirects to GET).
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // For now, redirect POST requests to GET
        response.sendRedirect(request.getContextPath() + "/");
    }
    
    /**
     * Prepares data to be displayed on the main page.
     * This method sets up all the dynamic content for the JSP.
     */
    private void preparePageData(HttpServletRequest request) {
        
        // Set the main title with dynamic versioning
        request.setAttribute("pageTitle", "Pipeline Testing Hub v2.0");

        // Set the main description content with enhanced messaging
        String description = "🚀 Welcome to the ultimate Pipeline Testing Experience! This cutting-edge Java web application " +
                           "demonstrates modern DevOps practices with stunning visual effects and real-time monitoring. " +
                           "Experience automated testing, continuous integration, and deployment pipeline validation " +
                           "in a beautifully crafted interface that showcases the power of modern web technologies " +
                           "combined with robust Java backend architecture.";
        request.setAttribute("description", description);
        
        // Set current timestamp for cache busting and display
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        request.setAttribute("timestamp", timestamp);
        
        // Set application version
        request.setAttribute("appVersion", "1.0.0");
        
        // Set enhanced pipeline testing concepts with emojis and detailed descriptions
        String[] pipelineConcepts = {
            "🔍 Automated Quality Checks: Real-time code analysis with SonarQube integration and quality gates",
            "🏗️ Build Validation: Maven/Gradle builds with dependency scanning and vulnerability detection",
            "🧪 Test Automation: Comprehensive testing suite including unit, integration, and E2E tests",
            "🚀 Deployment Readiness: Blue-green deployments with automated rollback capabilities",
            "📊 Performance Monitoring: Real-time metrics, logging, and application performance insights",
            "🔒 Security Scanning: OWASP dependency checks and container security validation",
            "📦 Artifact Management: Automated versioning and artifact repository integration",
            "🌐 Multi-Environment Support: Seamless deployment across dev, staging, and production"
        };
        request.setAttribute("pipelineConcepts", pipelineConcepts);
        
        // Set environment information
        request.setAttribute("javaVersion", System.getProperty("java.version"));
        request.setAttribute("serverInfo", getServletContext().getServerInfo());
    }
    
    /**
     * Returns information about this servlet.
     */
    @Override
    public String getServletInfo() {
        return "Pipeline Testing Servlet - Demonstrates pipeline testing concepts in a Java web application";
    }
}
