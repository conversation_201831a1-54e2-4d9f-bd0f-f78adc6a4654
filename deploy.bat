@echo off
echo ========================================
echo Pipeline Testing App - Deployment Script
echo ========================================

REM Check if WAR file exists
if not exist "target\pipeline-testing-app.war" (
    echo ERROR: WAR file not found. Please run 'mvn clean package' first.
    pause
    exit /b 1
)

echo WAR file found: target\pipeline-testing-app.war

REM Common Tomcat webapps directories (modify as needed)
set TOMCAT_WEBAPPS_1=C:\Program Files\Apache Software Foundation\Tomcat 9.0\webapps
set TOMCAT_WEBAPPS_2=C:\apache-tomcat-9.0.95\webapps
set TOMCAT_WEBAPPS_3=C:\tomcat\webapps

echo.
echo Checking for Tomcat webapps directory...

REM Check each possible location
if exist "%TOMCAT_WEBAPPS_1%" (
    set TOMCAT_WEBAPPS=%TOMCAT_WEBAPPS_1%
    goto :deploy
)

if exist "%TOMCAT_WEBAPPS_2%" (
    set TOMCAT_WEBAPPS=%TOMCAT_WEBAPPS_2%
    goto :deploy
)

if exist "%TOMCAT_WEBAPPS_3%" (
    set TOMCAT_WEBAPPS=%TOMCAT_WEBAPPS_3%
    goto :deploy
)

echo.
echo WARNING: Could not find Tomcat webapps directory automatically.
echo Please manually copy the WAR file to your Tomcat webapps directory:
echo.
echo   Source: %CD%\target\pipeline-testing-app.war
echo   Destination: [YOUR_TOMCAT_HOME]\webapps\
echo.
echo Common locations:
echo   - C:\Program Files\Apache Software Foundation\Tomcat X.X\webapps\
echo   - C:\apache-tomcat-X.X.X\webapps\
echo   - C:\tomcat\webapps\
echo.
pause
exit /b 1

:deploy
echo Found Tomcat webapps directory: %TOMCAT_WEBAPPS%

REM Remove old deployment if exists
if exist "%TOMCAT_WEBAPPS%\pipeline-testing-app.war" (
    echo Removing old WAR file...
    del "%TOMCAT_WEBAPPS%\pipeline-testing-app.war"
)

if exist "%TOMCAT_WEBAPPS%\pipeline-testing-app" (
    echo Removing old deployment directory...
    rmdir /s /q "%TOMCAT_WEBAPPS%\pipeline-testing-app"
)

REM Copy new WAR file
echo Copying new WAR file...
copy "target\pipeline-testing-app.war" "%TOMCAT_WEBAPPS%\"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo DEPLOYMENT SUCCESSFUL!
    echo ========================================
    echo.
    echo The application should be available at:
    echo   http://localhost:8080/pipeline-testing-app/
    echo.
    echo Available URLs:
    echo   - Home Page (JSP): http://localhost:8080/pipeline-testing-app/
    echo   - Servlet App:     http://localhost:8080/pipeline-testing-app/app
    echo   - Testing Page:    http://localhost:8080/pipeline-testing-app/home
    echo.
    echo Wait a few seconds for Tomcat to deploy the application...
) else (
    echo.
    echo ERROR: Failed to copy WAR file to Tomcat webapps directory.
    echo Please check permissions and try running as administrator.
)

echo.
pause
