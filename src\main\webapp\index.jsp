<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pipeline Testing App - Java Web Application</title>
    <meta name="description" content="A Java web application demonstrating pipeline testing concepts with dynamic visual effects">
    <meta name="keywords" content="java, servlet, jsp, pipeline-testing, web-application">
    <meta name="author" content="Pipeline Testing Demo">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="${pageContext.request.contextPath}/images/java-icon.svg">
    
    <!-- CSS Styles -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/app.css?v=${timestamp}">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="${pageContext.request.contextPath}/css/app.css" as="style">
</head>
<body>
    <div class="app">
        <!-- Navigation Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo-section">
                    <img src="${pageContext.request.contextPath}/images/java-icon.svg" alt="Java Logo" class="logo-icon">
                    <span class="app-name">Pipeline Testing App</span>
                </div>
                <nav class="nav-menu">
                    <a href="${pageContext.request.contextPath}/" class="nav-link active">Home</a>
                    <a href="${pageContext.request.contextPath}/home" class="nav-link">Testing</a>
                </nav>
            </div>
        </header>

        <main class="main-content">
            <!-- Main Title with Glowing Effect -->
            <h1 class="glowing-title">
                <c:out value="${pageTitle}" default="Pipeline Testing Application"/>
            </h1>
            
            <!-- Status Indicators -->
            <div class="status-bar">
                <div class="status-item status-success">
                    <span class="status-icon">✅</span>
                    <span class="status-text">Build Passing</span>
                </div>
                <div class="status-item status-info">
                    <span class="status-icon">🔄</span>
                    <span class="status-text">Tests Running</span>
                </div>
                <div class="status-item status-success">
                    <span class="status-icon">🚀</span>
                    <span class="status-text">Ready to Deploy</span>
                </div>
            </div>

            <!-- Main Content Section -->
            <div class="content-section">
                <p class="description">
                    <c:out value="${description}"/>
                </p>
                
                <!-- Additional Pipeline Concepts -->
                <c:if test="${not empty pipelineConcepts}">
                    <div class="concepts-section">
                        <h3 class="concepts-title">Key Pipeline Testing Concepts:</h3>
                        <ul class="concepts-list">
                            <c:forEach var="concept" items="${pipelineConcepts}">
                                <li class="concept-item">
                                    <c:out value="${concept}"/>
                                </li>
                            </c:forEach>
                        </ul>
                    </div>
                </c:if>
                
                <!-- Application Information -->
                <div class="app-info">
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">Application Version:</span>
                            <span class="info-value"><c:out value="${appVersion}"/></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Java Version:</span>
                            <span class="info-value"><c:out value="${javaVersion}"/></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Server:</span>
                            <span class="info-value"><c:out value="${serverInfo}"/></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Last Updated:</span>
                            <span class="info-value"><c:out value="${timestamp}"/></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <footer class="app-footer">
                <p class="footer-text">
                    Built with Java Servlets & JSP | 
                    <a href="${pageContext.request.contextPath}/" class="footer-link">Pipeline Testing Demo</a>
                </p>
            </footer>
        </main>
    </div>
    
    <!-- JavaScript for animations and interactions -->
    <script src="${pageContext.request.contextPath}/js/app.js?v=${timestamp}"></script>
</body>
</html>
